import React from "react";

type ExpensoBrandProps = {
  size?: "sm" | "md" | "lg"; // optional prop with 3 levels
};

const ExpensoBrand = ({ size = "md" }: ExpensoBrandProps) => {
  const logoSize = {
    sm: "w-10 h-10",
    md: "w-14 h-14",
    lg: "w-20 h-20",
  }[size];

  const textSize = {
    sm: "h-10",
    md: "h-14",
    lg: "h-20",
  }[size];

  const subtitleSize = {
    sm: "text-sm tracking-[0.7em]",
    md: "text-base tracking-[1.0em]",
    lg: "text-lg tracking-[1.5em]",
  }[size];

  return (
    <div className="mb-16">
      <div className="flex items-center justify-center mb-4">
        {/* Logo Icon */}
        <div className={`mr-4 ${logoSize}`}>
          <img
            src="/expenso-logo.png"
            alt="Expenso Logo"
            className="w-full h-full"
          />
        </div>

        {/* Logo Text as SVG */}
        <div className={textSize}>
          <img
            src="/expenso-text.svg"
            alt="EXPENSO"
            className="w-full h-full"
          />
        </div>
      </div>

      <p
        className={`text-gray-500 text-center font-medium uppercase ${subtitleSize}`}
      >
        Admin Dashboard
      </p>
    </div>
  );
};

export default ExpensoBrand;
