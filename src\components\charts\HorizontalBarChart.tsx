import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
} from 'recharts';

type ChartData = {
  label: string;
  value: number;
};

interface HorizontalBarChartProps {
  data: ChartData[];
  barColorList?: string[];
  maxValue?: number;
}

// Type for Bar shape props based on Recharts Bar component
interface BarShapeProps {
  x: number;
  y: number;
  width: number;
  height: number;
  fill?: string;
  index: number;
  payload: ChartData;
}

const defaultColors = ['#a0a0a0'];

const HorizontalBarChart: React.FC<HorizontalBarChartProps> = ({
  data,
  barColorList = defaultColors,
  maxValue,
}) => {
  const computedMax = maxValue ?? Math.max(...data.map((d) => d.value));
  const CustomBarShape = (props: BarShapeProps) => {
    const { x, y, width, height, fill } = props;
    return (
      <>
        <rect x={x} y={y} width={width} height={height} fill={fill} />
        <rect
          x={x + width - 12}
          y={y}
          width={12}
          height={height}
          fill="green"
        />
      </>
    );
  };

  return (
    <ResponsiveContainer width="100%" height={data.length * 40}>
      <BarChart
        layout="vertical"
        data={data}
        margin={{ top: 10, right: 20, left: 20, bottom: 10 }}
      >
        <XAxis
          type="number"
          domain={[0, computedMax]}
          axisLine={false}
          tickLine={false}
          hide
        />
        <YAxis
          dataKey="label"
          type="category"
          width={100}
          tick={{ fontSize: 12 }}
          axisLine={false}
          tickLine={false}
        />
        <Tooltip />
        <Bar
          dataKey="value"
          shape={(props: BarShapeProps) => (
            <CustomBarShape {...props} fill={barColorList[props.index % barColorList.length]} />
          )}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default HorizontalBarChart;
