import React from "react";
import type { IconType } from "react-icons";

type ButtonProps = {
  children: React.ReactNode;
  iconLeft?: IconType;
  iconRight?: IconType;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
  type?: "button" | "submit" | "reset";
};

export const Button: React.FC<ButtonProps> = ({
  children,
  iconLeft: IconLeft,
  iconRight: IconRight,
  isLoading = false,
  disabled = false,
  className = "",
  onClick,
  type = "button",
}) => {
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`inline-flex cursor-pointer items-center gap-2 rounded-md font-medium transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ${className}`}
    >
      {isLoading && (
        <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
      )}
      {IconLeft && <IconLeft className="h-5 w-5" />}
      {children}
      {IconRight && <IconRight className="h-5 w-5" />}
    </button>
  );
};
