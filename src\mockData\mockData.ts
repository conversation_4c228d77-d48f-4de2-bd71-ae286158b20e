interface User {
    id: string;
    name: string;
    email: string;
    department: string;
    role: string;
    status: 'Active' | 'Inactive';
    lastLogin: string;
    avatar?: string;
}

export const dummyUserTableData: User[] = [
    {
        id: "1",
        name: "<PERSON>",
        email: "<EMAIL>",
        department: "HR",
        role: "Manager",
        status: "Active",
        lastLogin: "2025-07-14",
    },
    {
        id: "2",
        name: "<PERSON>",
        email: "<EMAIL>",
        department: "Finance",
        role: "Analyst",
        status: "Inactive",
        lastLogin: "2025-06-29",
    },
    {
        id: "3",
        name: "<PERSON>",
        email: "<EMAIL>",
        department: "B<PERSON>",
        role: "Specialist",
        status: "Active",
        lastLogin: "2025-07-10",
    },
    {
        id: "4",
        name: "<PERSON>",
        email: "<EMAIL>",
        department: "HR",
        role: "Analyst",
        status: "Inactive",
        lastLogin: "2025-05-20",
    },
    {
        id: "5",
        name: "<PERSON>",
        email: "<EMAIL>",
        department: "Finance",
        role: "Manager",
        status: "Active",
        lastLogin: "2025-07-16",
    },
    {
        id: "6",
        name: "<PERSON> White",
        email: "<EMAIL>",
        department: "BI",
        role: "Specialist",
        status: "Active",
        lastLogin: "2025-07-15",
    },
    {
        id: "7",
        name: "George <PERSON>",
        email: "<EMAIL>",
        department: "HR",
        role: "Manager",
        status: "Inactive",
        lastLogin: "2025-06-01",
    },
    {
        id: "8",
        name: "Hannah Green",
        email: "<EMAIL>",
        department: "Finance",
        role: "Analyst",
        status: "Active",
        lastLogin: "2025-07-11",
    },
    {
        id: "9",
        name: "Isaac Lee",
        email: "<EMAIL>",
        department: "BI",
        role: "Specialist",
        status: "Inactive",
        lastLogin: "2025-07-03",
    },
    {
        id: "10",
        name: "Jenna Patel",
        email: "<EMAIL>",
        department: "HR",
        role: "Analyst",
        status: "Active",
        lastLogin: "2025-07-13",
    },
];

export const userRoleData = [
    { role: 'Manager', count: 45 },
    { role: 'Finance', count: 32 },
    { role: 'Analyst', count: 28 },
    { role: 'Lead', count: 25 },
    { role: 'Specialist', count: 18 },
    { role: 'Coordinator', count: 12 },
    { role: 'Designer', count: 8 }
];
