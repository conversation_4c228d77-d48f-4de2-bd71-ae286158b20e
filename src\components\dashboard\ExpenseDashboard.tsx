import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Bell,
  TrendingUp,
  TrendingDown,
  User,
  FileText,
  Check<PERSON><PERSON>cle,
  Settings,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON>A<PERSON>s,
  <PERSON>sponsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Tooltip,
} from "recharts";
import { RiBarChartFill } from "react-icons/ri";
import { FaCaretUp } from "react-icons/fa";
import { FaRegCalendar } from "react-icons/fa";
import { FaCheck } from "react-icons/fa6";
// Types for API data
interface DashboardStats {
  totalExpensesClaimed: number;
  totalReimbursementsPaid: number;
  pendingRequests: number;
  averageReimburseTime: number;
  expensesClaimedChange: number;
  reimbursementsPaidChange: number;
  pendingRequestsChange: number;
  averageTimeChange: number;
}

interface ExpenseTrendData {
  month: string;
  amount: number;
  budget: number;
  previous: number;
}

interface RecentActivity {
  id: string;
  type: "expense_report" | "reimbursement" | "system_update";
  title: string;
  submittedBy: string;
  date: string;
  status: "pending" | "approved" | "completed";
}

interface UserActivity {
  id: string;
  type:
    | "user_registered"
    | "expense_submitted"
    | "reimbursement_approved"
    | "system_updated"
    | "policy_updated";
  title: string;
  subtitle: string;
  timestamp: string;
  icon: "user" | "file" | "check" | "settings";
}

interface PolicyCompliance {
  percentage: number;
  withinPolicy: number;
  total: number;
}

// Mock data - replace with API calls
const mockDashboardStats: DashboardStats = {
  totalExpensesClaimed: 1250000,
  totalReimbursementsPaid: 1100000,
  pendingRequests: 75,
  averageReimburseTime: 3,
  expensesClaimedChange: 10,
  reimbursementsPaidChange: 8,
  pendingRequestsChange: -5,
  averageTimeChange: -1,
};

const mockExpenseTrend: ExpenseTrendData[] = [
  { month: "SEP", amount: 55000, budget: 40000, previous: 12000 },
  { month: "OCT", amount: 80000, budget: 40000, previous: 40000 },
  { month: "NOV", amount: 25000, budget: 40000, previous: 38000 },
  { month: "DEC", amount: 82000, budget: 40000, previous: 35000 },
  { month: "JAN", amount: 48000, budget: 40000, previous: 36000 },
  { month: "FEB", amount: 12000, budget: 40000, previous: 50000 },
];

const graphdata = [
  { month: "SEP", value: 115, budget: 105 },
  { month: "OCT", value: 95, budget: 105 },
  { month: "NOV", value: 165, budget: 115 },
  { month: "DEC", value: 95, budget: 105 },
  { month: "JAN", value: 110, budget: 120 },
  { month: "FEB", value: 115, budget: 125 },
];

const mockRecentActivity: RecentActivity[] = [
  {
    id: "1",
    type: "expense_report",
    title: "New Expense Report",
    submittedBy: "Emma Bennett",
    date: "2024-07-28",
    status: "pending",
  },
  {
    id: "2",
    type: "expense_report",
    title: "New Expense Report",
    submittedBy: "Emma Bennett",
    date: "2024-07-28",
    status: "pending",
  },
  {
    id: "3",
    type: "expense_report",
    title: "New Expense Report",
    submittedBy: "Emma Bennett",
    date: "2024-07-28",
    status: "pending",
  },
  {
    id: "4",
    type: "expense_report",
    title: "New Expense Report",
    submittedBy: "Emma Bennett",
    date: "2024-07-28",
    status: "pending",
  },
];

const mockUserActivity: UserActivity[] = [
  {
    id: "1",
    type: "user_registered",
    title: "New user registered: Sophia Turner",
    subtitle: "2 hours ago",
    timestamp: "2024-07-15T10:00:00Z",
    icon: "user",
  },
  {
    id: "2",
    type: "expense_submitted",
    title: "High-value expense report submitted by Liam Harper",
    subtitle: "4 hours ago",
    timestamp: "2024-07-15T08:00:00Z",
    icon: "file",
  },
  {
    id: "3",
    type: "reimbursement_approved",
    title: "Reimbursement request approved for Ava Bennett",
    subtitle: "6 hours ago",
    timestamp: "2024-07-15T06:00:00Z",
    icon: "check",
  },
  {
    id: "4",
    type: "system_updated",
    title: "System settings updated by Admin",
    subtitle: "1 day ago",
    timestamp: "2024-07-14T12:00:00Z",
    icon: "settings",
  },
  {
    id: "5",
    type: "policy_updated",
    title: "Expense policy updated",
    subtitle: "2 days ago",
    timestamp: "2024-07-13T15:00:00Z",
    icon: "file",
  },
];

const mockPolicyCompliance: PolicyCompliance = {
  percentage: 66,
  withinPolicy: 66,
  total: 100,
};

// Utility functions
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const formatNumber = (num: number): string => {
  return new Intl.NumberFormat("en-US").format(num);
};

// Components
const StatCard: React.FC<{
  title: string;
  value: string;
  change: number;
  isTime?: boolean;
  delay?: number;
}> = ({ title, value, change, isTime = false, delay = 0 }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delay);
    return () => clearTimeout(timer);
  }, [delay]);

  const changeColor = change > 0 ? "text-green-500" : "text-red-500";
  const changeIcon =
    change > 0 ? (
      <TrendingUp className="w-4 h-4" />
    ) : (
      <TrendingDown className="w-4 h-4" />
    );
  const changeText =
    change > 0
      ? `+${change}${isTime ? " day" : "%"}`
      : `${change}${isTime ? " day" : "%"}`;

  return (
    <div
      className={`bg-white rounded-xl p-6 shadow-sm transition-all duration-500 transform ${
        isVisible ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
      }`}
    >
      <h3 className="text-gray-600 text-sm mb-2">{title}</h3>
      <div className="flex items-end justify-between">
        <div className="text-2xl font-bold text-gray-900">{value}</div>
        <div className={`flex items-center gap-1 text-sm ${changeColor}`}>
          {changeIcon}
          {changeText}
        </div>
      </div>
    </div>
  );
};

const ExpenseTrendChart: React.FC<{ data: ExpenseTrendData[] }> = ({
  data,
}) => {
  const [animationProgress, setAnimationProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
      setAnimationProgress(1);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  const currentAmount = data[data.length - 1]?.amount || 0;

  // Add highlighted point for NOV
  const chartData = data.map((item, index) => ({
    ...item,
    highlighted: index === 2, // NOV point
  }));

  const CustomDot = (props: any) => {
    const { cx, cy, payload, dataKey } = props;

    // Only show special dot for the main amount line and highlighted point
    if (dataKey === "amount" && payload.highlighted) {
      return (
        <g>
          {/* Glow effect */}
          <circle
            cx={cx}
            cy={cy}
            r={8}
            fill="none"
            stroke="#14b8a6"
            strokeWidth={2}
            opacity={0.3}
          />
          <circle
            cx={cx}
            cy={cy}
            r={6}
            fill="#14b8a6"
            stroke="white"
            strokeWidth={2}
          />
          {/* Tooltip */}
          <rect
            x={cx - 25}
            y={cy - 35}
            width={50}
            height={20}
            rx={4}
            fill="#14b8a6"
          />
          <text
            x={cx}
            y={cy - 22}
            textAnchor="middle"
            fill="white"
            fontSize="12"
            fontWeight="bold"
          >
            $109.00
          </text>
        </g>
      );
    }

    // Regular dots for all lines
    const colors = {
      amount: "#14b8a6",
      budget: "#f59e0b",
      previous: "#8b5cf6",
    };

    return (
      <circle
        cx={cx}
        cy={cy}
        r={3}
        fill={colors[dataKey as keyof typeof colors] || "#14b8a6"}
        stroke="white"
        strokeWidth={2}
      />
    );
  };

  return (
    <div
      className={`bg-white rounded-xl p-6 shadow-sm transition-all duration-500 transform ${
        isVisible ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
      }`}
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Expense Trend</h3>
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-3 h-0.5 bg-teal-500 rounded-full"></div>
            <span>Current</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-0.5 bg-amber-500 rounded-full"></div>
            <span>Budget</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-0.5 bg-purple-500 rounded-full"></div>
            <span>Previous</span>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <div className="text-3xl font-bold text-gray-900">
          ${(currentAmount / 1000).toFixed(1)}K
        </div>
        <div className="text-sm text-gray-600 mb-2">Total Spent</div>
        <div className="flex items-center gap-1 text-sm text-green-500 mb-3">
          <TrendingUp className="w-4 h-4" />
          +2.45%
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-sm text-gray-600">On track</span>
        </div>
      </div>

      <div className="h-48">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 0, bottom: 0 }}
          >
            <defs>
              <linearGradient
                id="currentLineGradient"
                x1="0"
                y1="0"
                x2="1"
                y2="0"
              >
                <stop offset="0%" stopColor="#14b8a6" stopOpacity={0.8} />
                <stop offset="100%" stopColor="#14b8a6" stopOpacity={1} />
              </linearGradient>
              <linearGradient
                id="budgetLineGradient"
                x1="0"
                y1="0"
                x2="1"
                y2="0"
              >
                <stop offset="0%" stopColor="#f59e0b" stopOpacity={0.8} />
                <stop offset="100%" stopColor="#f59e0b" stopOpacity={1} />
              </linearGradient>
              <linearGradient
                id="previousLineGradient"
                x1="0"
                y1="0"
                x2="1"
                y2="0"
              >
                <stop offset="0%" stopColor="#8b5cf6" stopOpacity={0.8} />
                <stop offset="100%" stopColor="#8b5cf6" stopOpacity={1} />
              </linearGradient>
              <filter id="glowCurrent">
                <feGaussianBlur stdDeviation="2" result="coloredBlur" />
                <feMerge>
                  <feMergeNode in="coloredBlur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>
              <filter id="glowBudget">
                <feGaussianBlur stdDeviation="1.5" result="coloredBlur" />
                <feMerge>
                  <feMergeNode in="coloredBlur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>
              <filter id="glowPrevious">
                <feGaussianBlur stdDeviation="1.5" result="coloredBlur" />
                <feMerge>
                  <feMergeNode in="coloredBlur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>
            </defs>
            <XAxis
              dataKey="month"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#9ca3af" }}
            />
            <YAxis hide />

            {/* Current Expenses Line (main line with glow) */}
            <Line
              type="monotone"
              dataKey="amount"
              stroke="url(#currentLineGradient)"
              strokeWidth={3}
              dot={<CustomDot />}
              filter="url(#glowCurrent)"
              strokeDasharray={animationProgress ? "0" : "1000"}
              strokeDashoffset={animationProgress ? "0" : "1000"}
              style={{
                transition:
                  "stroke-dashoffset 2s ease-in-out, stroke-dasharray 2s ease-in-out",
              }}
            />

            {/* Budget Line */}
            <Line
              type="monotone"
              dataKey="budget"
              stroke="url(#budgetLineGradient)"
              strokeWidth={3}
              dot={<CustomDot />}
              filter="url(#glowBudget)"
              strokeDasharray={animationProgress ? "0" : "1000"}
              strokeDashoffset={animationProgress ? "0" : "1000"}
              style={{
                transition: "stroke-dashoffset 2.2s ease-in-out",
                transitionDelay: "0.3s",
              }}
            />

            {/* Previous Period Line */}
            <Line
              type="monotone"
              dataKey="previous"
              stroke="url(#previousLineGradient)"
              strokeWidth={2}
              dot={<CustomDot />}
              filter="url(#glowPrevious)"
              strokeDasharray={animationProgress ? "0" : "1000"}
              strokeDashoffset={animationProgress ? "0" : "1000"}
              style={{
                transition: "stroke-dashoffset 2.4s ease-in-out",
                transitionDelay: "0.3s",
              }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

const PolicyComplianceChart: React.FC<{ data: PolicyCompliance }> = ({
  data,
}) => {
  const [animationProgress, setAnimationProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
      setAnimationProgress(1);
    }, 800);
    return () => clearTimeout(timer);
  }, []);

  const remainingPercentage = 100 - data.percentage;

  const pieData = [
    {
      name: "Within Policy",
      value: data.percentage,
      color: "#10b981",
    },
    {
      name: "Outside Policy",
      value: remainingPercentage,
      color: "#e5e7eb",
    },
  ];

  const renderCustomizedLabel = () => null;

  return (
    <div
      className={`bg-white rounded-xl p-6  shadow-sm transition-all duration-500 transform ${
        isVisible ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
      }`}
    >
      <h3 className="text-lg font-semibold text-gray-900 mb-6">
        Policy Compliance
      </h3>

      <div className="flex flex-col items-center">
        <div className="relative w-48 h-7 mb-2">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                innerRadius={55}
                outerRadius={75}
                startAngle={90}
                endAngle={450}
                paddingAngle={0}
                dataKey="value"
                labelLine={false}
                label={renderCustomizedLabel}
                animationDuration={1000}
                animationBegin={0}
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>

          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <div className="text-2xl font-bold text-gray-900">
              {Math.round(data.percentage * animationProgress)}%
            </div>
            <div className="text-xs text-gray-600 text-center">
              Within Policy
            </div>
          </div>
        </div>

        <div className="w-full space-y-2 text-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-gray-600">Within Policy</span>
            </div>
            <span className="font-medium text-gray-900">
              {data.percentage}%
            </span>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <span className="text-gray-600">Outside Policy</span>
            </div>
            <span className="font-medium text-gray-900">
              {remainingPercentage}%
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

const ActivityList: React.FC<{ activities: RecentActivity[] }> = ({
  activities,
}) => {
  return (
    <div className="bg-white rounded-xl p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">
        Recent Activity
      </h3>

      <div className="space-y-4">
        <div className="grid grid-cols-4 gap-4 text-sm font-medium text-gray-600 pb-2 border-b">
          <div>Activity</div>
          <div>Details</div>
          <div>Date</div>
          <div>Actions</div>
        </div>

        {activities.map((activity, index) => (
          <div
            key={activity.id}
            className={`grid grid-cols-4 gap-4 text-sm py-3 transition-all duration-300 transform ${
              index < 4
                ? "translate-x-0 opacity-100"
                : "translate-x-4 opacity-0"
            }`}
            style={{ transitionDelay: `${index * 100}ms` }}
          >
            <div className="font-medium text-gray-900">{activity.title}</div>
            <div className="text-gray-600">
              Submitted by {activity.submittedBy}
            </div>
            <div className="text-gray-600">{activity.date}</div>
            <div>
              <button className="text-teal-600 hover:text-teal-700 font-medium">
                view
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const UserActivityFeed: React.FC<{ activities: UserActivity[] }> = ({
  activities,
}) => {
  const getIcon = (iconType: string) => {
    switch (iconType) {
      case "user":
        return <User className="w-5 h-5" />;
      case "file":
        return <FileText className="w-5 h-5" />;
      case "check":
        return <CheckCircle className="w-5 h-5" />;
      case "settings":
        return <Settings className="w-5 h-5" />;
      default:
        return <User className="w-5 h-5" />;
    }
  };

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">
        User Activity
      </h3>
      <div className="space-y-4">
        {activities.map((activity, index) => (
          <div
            key={activity.id}
            className={`flex items-start gap-3 transition-all duration-300 transform ${
              index < 5
                ? "translate-x-0 opacity-100"
                : "translate-x-4 opacity-0"
            }`}
            style={{ transitionDelay: `${index * 150}ms` }}
          >
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 flex-shrink-0">
              {getIcon(activity.icon)}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900">
                {activity.title}
              </p>
              <p className="text-xs text-gray-500 mt-1">{activity.subtitle}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Main Dashboard Component
const ExpenseDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] =
    useState<DashboardStats>(mockDashboardStats);
  const [expenseTrendData, setExpenseTrendData] =
    useState<ExpenseTrendData[]>(mockExpenseTrend);
  const [recentActivity, setRecentActivity] =
    useState<RecentActivity[]>(mockRecentActivity);
  const [userActivity, setUserActivity] =
    useState<UserActivity[]>(mockUserActivity);
  const [policyCompliance, setPolicyCompliance] =
    useState<PolicyCompliance>(mockPolicyCompliance);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate API loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  // API integration functions (replace with actual API calls)
  const fetchDashboardData = async () => {
    try {
      // const response = await fetch('/api/dashboard/stats');
      // const data = await response.json();
      // setDashboardData(data);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    }
  };

  const fetchExpenseTrend = async () => {
    try {
      // const response = await fetch('/api/expenses/trend');
      // const data = await response.json();
      // setExpenseTrendData(data);
    } catch (error) {
      console.error("Error fetching expense trend:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 border-4 border-teal-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-gray-600">Loading dashboard...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen min-w-full bg-red-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-teal-500 rounded-lg flex items-center justify-center">
                <div className="w-6 h-6 bg-white rounded opacity-80"></div>
              </div>
              <h1 className="text-xl font-semibold text-gray-900">
                Tech Solutions Inc.
              </h1>
            </div>

            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search"
                  className="pl-10 pr-4 py-2 bg-gray-100 rounded-lg border-none focus:outline-none focus:ring-2 focus:ring-teal-500 w-64"
                />
              </div>
              <button className="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center text-white hover:bg-teal-600 transition-colors">
                <Bell className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-fit mx-auto px-6 py-8 bg-amber-300">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Total Expenses Claimed"
            value={formatCurrency(dashboardData.totalExpensesClaimed)}
            change={dashboardData.expensesClaimedChange}
            delay={0}
          />
          <StatCard
            title="Total Reimbursements Paid"
            value={formatCurrency(dashboardData.totalReimbursementsPaid)}
            change={dashboardData.reimbursementsPaidChange}
            delay={100}
          />
          <StatCard
            title="Pending Requests"
            value={formatNumber(dashboardData.pendingRequests)}
            change={dashboardData.pendingRequestsChange}
            delay={200}
          />
          <StatCard
            title="Average Reimburse Time"
            value={`${dashboardData.averageReimburseTime} days`}
            change={dashboardData.averageTimeChange}
            isTime={true}
            delay={300}
          />
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2">
            {/* <ExpenseTrendChart data={expenseTrendData} /> */}

            <div className="bg-white rounded-2xl p-4 sm:p-6 shadow-lg lg:row-span-2">
              {/* Header */}
              <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-12 gap-4">
                <div className="text-sm sm:text-md font-bold text-gray-800">
                  Historical Trend
                </div>
                <div className="flex items-center gap-2 sm:gap-3">
                  <button
                    className="px-3 sm:px-5 py-2 sm:py-2.5 rounded-lg text-xs text-gray-400 flex items-center gap-2 sm:gap-3 bg-blue-50"
                    onClick={() => {}}
                  >
                    <FaRegCalendar className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400" />
                    <span className="hidden sm:inline">This Year</span>
                    <span className="sm:hidden">Year</span>
                  </button>
                  <button
                    className="p-2 rounded-lg text-gray-500 bg-blue-50"
                    onClick={() => {}}
                  >
                    <RiBarChartFill className="w-3 h-3 sm:w-4 sm:h-4 text-teal-600" />
                  </button>
                </div>
              </div>

              {/* Metrics */}
              <div className="mb-6 sm:mb-8">
                <div className="mb-2">
                  <span className="text-2xl sm:text-3xl font-bold text-gray-500">
                    $37.5K
                  </span>
                </div>
                <div className="flex items-baseline gap-2 sm:gap-3">
                  <div className="text-xs sm:text-sm text-gray-400 mb-4 font-semibold">
                    Total Spent
                  </div>
                  <div className="text-xs sm:text-sm text-green-400 font-bold flex items-center gap-1">
                    <FaCaretUp />
                    <span>+2.45%</span>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-green-400 p-1 rounded-full flex items-center justify-center">
                    <FaCheck className="text-white w-3 h-3" />
                  </div>
                  <span className="text-sm sm:text-md font-bold text-green-400">
                    On track
                  </span>
                </div>
              </div>

              <div className="h-32 sm:h-48 relative">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={graphdata}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <XAxis
                      dataKey="month"
                      axisLine={false}
                      tickLine={false}
                      tick={{
                        fontSize: 14,
                        fill: "#9CA3AF",
                        fontWeight: "bold",
                      }}
                      dy={10}
                    />
                    <YAxis hide />

                    <Line
                      type="monotone"
                      dataKey="budget"
                      stroke="#08ccb5"
                      strokeWidth={3}
                      dot={false}
                      strokeDasharray="none"
                      activeDot={{
                        r: 4,
                        fill: "white",
                        stroke: "#08ccb5",
                        strokeWidth: 2,
                      }}
                    />

                    <Line
                      type="monotone"
                      dataKey="value"
                      stroke="#009191"
                      strokeWidth={3}
                      dot={false}
                      activeDot={{
                        r: 4,
                        fill: "white",
                        stroke: "#009191",
                        strokeWidth: 2,
                      }}
                    />

                    <Tooltip
                      content={({ active, payload }) => {
                        if (active && payload && payload.length) {
                          return (
                            <div className="bg-teal-500 text-white p-2 sm:p-3 rounded-lg text-xs font-medium shadow-lg">
                              <div className="flex flex-col gap-1">
                                <div>Actual: ${payload[0].value}.00</div>
                                <div>Budget: ${payload[1].value}.00</div>
                              </div>
                            </div>
                          );
                        }
                        return null;
                      }}
                      cursor={false}
                      allowEscapeViewBox={{ x: false, y: true }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
          <div>
            <PolicyComplianceChart data={policyCompliance} />
          </div>
        </div>

        {/* Activity Tables */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <ActivityList activities={recentActivity} />
          </div>
          <div>
            <UserActivityFeed activities={userActivity} />
          </div>
        </div>
      </main>
    </div>
  );
};

export default ExpenseDashboard;
