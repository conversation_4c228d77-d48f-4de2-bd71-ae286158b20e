import Layout from "../components/layout/MainLayout";
import AuditTrail from "../pages/AuditTrail/AuditTrail";
import Categories from "../pages/Categories/Categories";
import ExpenseReports from "../pages/ExpenseReports/ExpenseReports";
import Home from "../pages/Home/Home";
import Integrations from "../pages/Integrations/Integrations";
import LoginPage from "../pages/Login/Login";
import PolicyManagement from "../pages/PolicyManagement/PolicyManagement";
import Settings from "../pages/Settings/Settings";
import UserManagement from "../pages/UserManagement/UserManagement";

export const routes = [
    {
        path: "/login",
        element: <LoginPage />,
    },
    {
        path: "/",
        element: <Layout />,
        children: [
            {
                index: true,
                element: <Home />,
            },
            {
                path: "users",
                element: <UserManagement />,
            },
            {
                path: "expense-reports",
                element: <ExpenseReports />,
            },
            {
                path: "policies",
                element: <PolicyManagement />,
            },
            {
                path: "categories",
                element: <Categories />,
            },
            {
                path: "integrations",
                element: <Integrations />,
            },
            {
                path: "audit-trail",
                element: <AuditTrail />,
            },
            {
                path: "settings",
                element: <Settings />,
            },
        ],
    },
];